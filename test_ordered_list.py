#!/usr/bin/env python3
"""
测试Markdown有序列表渲染问题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from markdown_utils import render_markdown

def test_ordered_lists():
    """测试有序列表渲染"""
    print("🧪 测试Markdown有序列表渲染...")
    
    test_cases = [
        {
            'name': '简单有序列表',
            'input': '1. 第一项\n2. 第二项\n3. 第三项',
            'expected_tags': ['<ol>', '<li>', '</ol>', '</li>']
        },
        {
            'name': '有序列表与文本混合',
            'input': '这是一个列表：\n\n1. 第一项\n2. 第二项\n\n结束。',
            'expected_tags': ['<ol>', '<li>', '</ol>', '</li>']
        },
        {
            'name': '嵌套有序列表',
            'input': '1. 第一项\n   1. 子项1\n   2. 子项2\n2. 第二项',
            'expected_tags': ['<ol>', '<li>', '</ol>', '</li>']
        },
        {
            'name': '无序列表对比',
            'input': '- 第一项\n- 第二项\n- 第三项',
            'expected_tags': ['<ul>', '<li>', '</ul>', '</li>']
        },
        {
            'name': '混合列表',
            'input': '1. 有序第一项\n2. 有序第二项\n\n- 无序第一项\n- 无序第二项',
            'expected_tags': ['<ol>', '<ul>', '<li>', '</ol>', '</ul>', '</li>']
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 测试 {i}: {test_case['name']}")
        print(f"输入: {repr(test_case['input'])}")
        
        try:
            result = render_markdown(test_case['input'])
            print(f"输出: {result}")
            
            # 检查是否包含期望的标签
            missing_tags = []
            for tag in test_case['expected_tags']:
                if tag not in result:
                    missing_tags.append(tag)
            
            if not missing_tags:
                print(f"✅ 测试通过")
            else:
                print(f"❌ 测试失败 - 缺少标签: {missing_tags}")
                
        except Exception as e:
            print(f"❌ 测试异常: {e}")

def test_markdown_extensions():
    """测试Markdown扩展配置"""
    print("\n🔧 检查Markdown扩展配置...")
    
    import markdown
    from markdown_utils import markdown_renderer
    
    print(f"当前扩展: {markdown_renderer.extensions}")
    print(f"扩展配置: {markdown_renderer.extension_configs}")
    print(f"允许的标签: {markdown_renderer.allowed_tags}")
    
    # 测试原生markdown库
    print("\n🧪 测试原生markdown库...")
    md = markdown.Markdown()
    test_input = "1. 第一项\n2. 第二项\n3. 第三项"
    raw_output = md.convert(test_input)
    print(f"原生输出: {raw_output}")

def test_specific_list_formats():
    """测试特定的列表格式"""
    print("\n📋 测试特定列表格式...")
    
    formats = [
        "1. 项目一",
        "1. 项目一\n2. 项目二",
        "1. 项目一\n2. 项目二\n3. 项目三",
        "1.项目一\n2.项目二",  # 没有空格
        "1.  项目一\n2.  项目二",  # 两个空格
        "1) 项目一\n2) 项目二",  # 使用括号
    ]
    
    for i, fmt in enumerate(formats, 1):
        print(f"\n格式 {i}: {repr(fmt)}")
        result = render_markdown(fmt)
        print(f"结果: {result}")
        
        if '<ol>' in result and '<li>' in result:
            print("✅ 正确渲染为有序列表")
        elif '<ul>' in result and '<li>' in result:
            print("⚠️ 渲染为无序列表")
        else:
            print("❌ 未渲染为列表")

if __name__ == '__main__':
    test_ordered_lists()
    test_markdown_extensions()
    test_specific_list_formats()
