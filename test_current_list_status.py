#!/usr/bin/env python3
"""
测试当前列表渲染状态
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from markdown_utils import render_markdown

def test_current_status():
    """测试当前列表渲染状态"""
    print("🧪 测试当前Markdown列表渲染状态...")
    
    test_cases = [
        {
            'name': '简单有序列表',
            'input': '1. 第一项\n2. 第二项\n3. 第三项',
        },
        {
            'name': '简单无序列表',
            'input': '- 第一项\n- 第二项\n- 第三项',
        },
        {
            'name': '有序列表与段落',
            'input': '这是段落。\n\n1. 第一项\n2. 第二项\n\n另一个段落。',
        },
        {
            'name': '无序列表与段落',
            'input': '这是段落。\n\n- 第一项\n- 第二项\n\n另一个段落。',
        },
        {
            'name': '分离的列表',
            'input': '1. 有序项1\n2. 有序项2\n\n\n- 无序项1\n- 无序项2',
        },
        {
            'name': '嵌套列表',
            'input': '1. 第一项\n   - 子项1\n   - 子项2\n2. 第二项',
        },
        {
            'name': '带格式的列表',
            'input': '1. **粗体项**\n2. *斜体项*\n3. `代码项`',
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 测试 {i}: {test_case['name']}")
        print(f"输入: {repr(test_case['input'])}")
        
        try:
            result = render_markdown(test_case['input'])
            print(f"输出: {result}")
            
            # 分析结果
            if '<ol>' in result and '<li>' in result:
                print("✅ 包含有序列表")
            if '<ul>' in result and '<li>' in result:
                print("✅ 包含无序列表")
            if '<p>' in result:
                print("✅ 包含段落")
            if '<strong>' in result or '<em>' in result or '<code>' in result:
                print("✅ 包含格式化内容")
                
        except Exception as e:
            print(f"❌ 测试异常: {e}")

def test_problematic_cases():
    """测试有问题的情况"""
    print("\n🔍 测试有问题的情况...")
    
    # 这个应该产生两个分离的列表
    problematic_input = '1. 有序项1\n2. 有序项2\n\n- 无序项1\n- 无序项2'
    print(f"问题输入: {repr(problematic_input)}")
    result = render_markdown(problematic_input)
    print(f"实际输出: {result}")
    
    # 分析问题
    ol_count = result.count('<ol>')
    ul_count = result.count('<ul>')
    print(f"有序列表数量: {ol_count}")
    print(f"无序列表数量: {ul_count}")
    
    if ol_count == 1 and ul_count == 1:
        print("✅ 正确：两个分离的列表")
    elif ol_count == 1 and ul_count == 0:
        print("❌ 问题：无序列表被合并到有序列表中")
    else:
        print(f"❓ 未知情况：ol={ol_count}, ul={ul_count}")

def test_workarounds():
    """测试解决方案"""
    print("\n🛠️ 测试解决方案...")
    
    # 尝试不同的分隔方式
    workarounds = [
        {
            'name': '双空行分隔',
            'input': '1. 有序项1\n2. 有序项2\n\n\n- 无序项1\n- 无序项2'
        },
        {
            'name': '段落分隔',
            'input': '1. 有序项1\n2. 有序项2\n\n---\n\n- 无序项1\n- 无序项2'
        },
        {
            'name': '文本分隔',
            'input': '1. 有序项1\n2. 有序项2\n\n列表结束\n\n- 无序项1\n- 无序项2'
        }
    ]
    
    for workaround in workarounds:
        print(f"\n🔧 {workaround['name']}: {repr(workaround['input'])}")
        result = render_markdown(workaround['input'])
        print(f"结果: {result}")
        
        ol_count = result.count('<ol>')
        ul_count = result.count('<ul>')
        if ol_count == 1 and ul_count == 1:
            print("✅ 成功分离列表")
        else:
            print(f"❌ 仍有问题: ol={ol_count}, ul={ul_count}")

if __name__ == '__main__':
    test_current_status()
    test_problematic_cases()
    test_workarounds()
