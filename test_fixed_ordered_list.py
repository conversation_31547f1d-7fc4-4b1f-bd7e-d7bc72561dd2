#!/usr/bin/env python3
"""
测试修复后的Markdown有序列表渲染
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from markdown_utils import render_markdown

def test_fixed_ordered_lists():
    """测试修复后的有序列表渲染"""
    print("🧪 测试修复后的Markdown有序列表渲染...")
    
    test_cases = [
        {
            'name': '简单有序列表',
            'input': '1. 第一项\n2. 第二项\n3. 第三项',
            'should_contain': ['<ol>', '<li>第一项</li>', '<li>第二项</li>', '<li>第三项</li>', '</ol>'],
            'should_not_contain': ['<ul>']
        },
        {
            'name': '有序列表与文本混合',
            'input': '这是一个列表：\n\n1. 第一项\n2. 第二项\n\n结束。',
            'should_contain': ['<p>这是一个列表：</p>', '<ol>', '<li>第一项</li>', '<li>第二项</li>', '</ol>', '<p>结束。</p>'],
            'should_not_contain': ['<ul>']
        },
        {
            'name': '正确的嵌套有序列表',
            'input': '1. 第一项\n  1. 子项1\n  2. 子项2\n2. 第二项',
            'should_contain': ['<ol>', '<li>第一项', '<li>子项1</li>', '<li>子项2</li>', '<li>第二项</li>', '</ol>'],
            'should_not_contain': ['<ul>']
        },
        {
            'name': '无序列表对比',
            'input': '- 第一项\n- 第二项\n- 第三项',
            'should_contain': ['<ul>', '<li>第一项</li>', '<li>第二项</li>', '<li>第三项</li>', '</ul>'],
            'should_not_contain': ['<ol>']
        },
        {
            'name': '正确分隔的混合列表',
            'input': '1. 有序第一项\n2. 有序第二项\n\n- 无序第一项\n- 无序第二项',
            'should_contain': ['<ol>', '<li>有序第一项</li>', '<li>有序第二项</li>', '</ol>', '<ul>', '<li>无序第一项</li>', '<li>无序第二项</li>', '</ul>'],
            'should_not_contain': []
        },
        {
            'name': '带格式的列表项',
            'input': '1. **粗体项目**\n2. *斜体项目*\n3. `代码项目`',
            'should_contain': ['<ol>', '<strong>粗体项目</strong>', '<em>斜体项目</em>', '<code>代码项目</code>', '</ol>'],
            'should_not_contain': ['<ul>']
        }
    ]
    
    passed = 0
    failed = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 测试 {i}: {test_case['name']}")
        print(f"输入: {repr(test_case['input'])}")
        
        try:
            result = render_markdown(test_case['input'])
            print(f"输出: {result}")
            
            # 检查应该包含的内容
            missing_content = []
            for content in test_case['should_contain']:
                if content not in result:
                    missing_content.append(content)
            
            # 检查不应该包含的内容
            unwanted_content = []
            for content in test_case['should_not_contain']:
                if content in result:
                    unwanted_content.append(content)
            
            if not missing_content and not unwanted_content:
                print(f"✅ 测试通过")
                passed += 1
            else:
                print(f"❌ 测试失败")
                if missing_content:
                    print(f"   缺少内容: {missing_content}")
                if unwanted_content:
                    print(f"   不应包含: {unwanted_content}")
                failed += 1
                
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            failed += 1
    
    print(f"\n📊 测试结果: {passed} 通过, {failed} 失败")
    return failed == 0

def test_list_edge_cases():
    """测试列表的边缘情况"""
    print("\n🔍 测试列表边缘情况...")
    
    edge_cases = [
        {
            'name': '数字后没有空格',
            'input': '1.项目一\n2.项目二',
            'expected': '不应该渲染为列表'
        },
        {
            'name': '使用括号的数字',
            'input': '1) 项目一\n2) 项目二',
            'expected': '不应该渲染为列表'
        },
        {
            'name': '混合缩进',
            'input': '1. 项目一\n    - 子项目\n2. 项目二',
            'expected': '应该正确处理嵌套'
        },
        {
            'name': '空列表项',
            'input': '1. 第一项\n2. \n3. 第三项',
            'expected': '应该处理空项'
        }
    ]
    
    for case in edge_cases:
        print(f"\n📄 {case['name']}: {repr(case['input'])}")
        result = render_markdown(case['input'])
        print(f"结果: {result}")
        print(f"期望: {case['expected']}")

def test_line_break_handling():
    """测试换行处理"""
    print("\n🔄 测试换行处理...")
    
    test_cases = [
        {
            'name': '段落内换行',
            'input': '第一行\n第二行\n第三行',
            'should_contain': ['<br>']
        },
        {
            'name': '段落间空行',
            'input': '第一段\n\n第二段',
            'should_contain': ['<p>第一段</p>', '<p>第二段</p>']
        },
        {
            'name': '列表不受换行影响',
            'input': '1. 第一项\n2. 第二项',
            'should_contain': ['<ol>', '<li>第一项</li>', '<li>第二项</li>', '</ol>'],
            'should_not_contain': ['<br>']
        }
    ]
    
    for case in test_cases:
        print(f"\n📝 {case['name']}: {repr(case['input'])}")
        result = render_markdown(case['input'])
        print(f"结果: {result}")
        
        # 检查期望内容
        for content in case.get('should_contain', []):
            if content in result:
                print(f"✅ 包含: {content}")
            else:
                print(f"❌ 缺少: {content}")
        
        for content in case.get('should_not_contain', []):
            if content not in result:
                print(f"✅ 正确排除: {content}")
            else:
                print(f"❌ 不应包含: {content}")

if __name__ == '__main__':
    success = test_fixed_ordered_lists()
    test_list_edge_cases()
    test_line_break_handling()
    
    if success:
        print("\n🎉 有序列表修复验证成功！")
    else:
        print("\n❌ 有序列表仍有问题，需要进一步修复。")
    
    sys.exit(0 if success else 1)
