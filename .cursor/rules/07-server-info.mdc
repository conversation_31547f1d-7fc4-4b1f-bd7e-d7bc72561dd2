---
description: 
globs: 
alwaysApply: true
---
# 服务器信息

- **操作系统:** Ubghthouse`
- **主机:** `**************`
- **端口:** `22`
- **容器化:** 已安装 Docker
- **进程管理器:** 使用 `pm2` 统一管理所有后台服务 (`blog`, `haobbs`, `weibo`)。
- **Web 服务器/反向代理:** 使用 Caddy 支持 HTTPS (安装在宿主机上)

## Caddy 配置

- Caddy 配置文件路径: `/etc/caddy/Caddyfile`
- 重启 Caddy 服务命令: `sudo systemctl reload caddy`
- 当前配置的域名:
    - `weibo.haoxueren.com` -> `localhost:5001`
    - `bbs.haoxueren.com` -> `localhost:5002`
    - `blog.haoxueren.com` -> `localhost:5003`
