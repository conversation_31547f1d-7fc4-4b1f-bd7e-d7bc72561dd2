---
description: 
globs: 
alwaysApply: true
---
# 项目概览

这是一个使用Flask和SQLAlchemy构建的简单微博客Web应用。

## 主要文件和目录

- [app.py](mdc:app.py) - Flask应用主文件，包含所有路由和主要功能逻辑
- [models.py](mdc:models.py) - SQLAlchemy数据模型定义
- [templates/](mdc:templates) - HTML模板目录
- [static/](mdc:static) - 静态资源文件(CSS, JavaScript等)
- [weibo.db](mdc:weibo.db) - SQLite数据库文件
- [requirements.txt](mdc:requirements.txt) - Python依赖库清单

## 功能特性

- 用户登录和身份验证
- 发布、查看、编辑和删除微博
- 为微博添加标签
- 按标签过滤微博
- 搜索微博内容和标签
- 随机浏览微博
- 文件上传功能(最大100MB)
