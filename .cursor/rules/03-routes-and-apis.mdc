---
description: 
globs: 
alwaysApply: true
---
# 路由和API端点

应用的所有路由和API端点都定义在 [app.py](mdc:app.py) 中。

## 主要页面路由

- `@app.route('/')` - 主页，显示微博列表，支持分页、标签过滤和关键词搜索
- `@app.route('/login')` - 用户登录页面
- `@app.route('/logout')` - 用户登出
- `@app.route('/random')` - 随机浏览微博页面
- `@app.route('/tags')` - 标签页面，显示所有标签及其使用次数

## 微博操作路由

- `@app.route('/post/create', methods=['POST'])` - 创建新微博
- `@app.route('/post/<int:post_id>/update', methods=['POST'])` - 更新微博
- `@app.route('/post/<int:post_id>/delete', methods=['POST'])` - 删除微博

## API端点

- `@app.route('/api/random_post')` - 获取随机微博的API
- `@app.route('/api/post/navigate')` - 导航上一条/下一条微博的API

## 文件上传

- `@app.route('/upload', methods=['POST'])` - 文件上传处理

## 身份验证

应用使用Flask的session机制管理用户登录状态，大多数路由都使用`@login_required`装饰器来保护，确保只有登录用户才能访问。
