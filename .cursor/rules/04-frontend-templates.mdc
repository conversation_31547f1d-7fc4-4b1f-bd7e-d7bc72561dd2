---
description: 
globs: 
alwaysApply: true
---
# 前端模板

应用使用Flask的Jinja2模板引擎渲染HTML页面。主要模板文件位于 [templates/](mdc:templates) 目录。

## 主要模板文件

- [templates/index.html](mdc:templates/index.html) - 主页模板，显示微博列表，包含发布微博的表单和微博展示区域
- [templates/login.html](mdc:templates/login.html) - 登录页面模板
- [templates/random.html](mdc:templates/random.html) - 随机浏览微博页面模板
- [templates/tags.html](mdc:templates/tags.html) - 标签页面模板，展示所有标签及其使用次数
- [templates/_edit_modal.html](mdc:templates/_edit_modal.html) - 编辑微博的模态窗口模板，被其他模板引用

## 静态资源

静态资源文件位于 [static/](mdc:static) 目录，包括：

- CSS样式文件
- JavaScript脚本
- 图标和其他媒体文件

## JavaScript功能

主要前端交互功能包括：

- 添加和删除标签
- AJAX微博发布和编辑
- 随机浏览微博的导航
- 文件上传预览和处理
