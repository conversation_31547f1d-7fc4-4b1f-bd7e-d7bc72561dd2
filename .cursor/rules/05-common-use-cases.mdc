---
description: 
globs: 
alwaysApply: true
---
# 常见用例和工作流

这个文件描述了应用的主要使用场景和操作流程。

## 用户认证流程

1. 用户访问应用，如未登录会被重定向到登录页面
2. 用户输入用户名和密码进行登录
3. 登录成功后，用户ID存储在session中，用户可以访问应用功能
4. 用户可以通过点击"退出"链接退出登录

## 微博发布流程

1. 用户在主页的文本区域输入微博内容
2. 用户可以添加标签（通过在文本中使用 `#标签名` 格式或使用标签输入框）
3. 用户点击"发布"按钮提交微博
4. 服务器处理请求，保存微博内容和标签到数据库
5. 页面自动刷新显示最新发布的微博

## 微博编辑流程

1. 用户点击微博旁边的"编辑"按钮
2. 弹出编辑模态窗口，显示微博当前内容和标签
3. 用户修改内容和标签
4. 用户点击"保存"按钮提交更改
5. 服务器更新数据库中的微博内容

## 随机浏览微博

1. 用户访问随机浏览页面（/random）
2. 系统随机选择一条微博显示给用户
3. 用户可以点击"上一条"或"下一条"按钮浏览其他微博

## 标签和搜索功能

1. 用户可以在主页上按标签筛选微博
2. 用户可以使用搜索框搜索微博内容或标签
3. 用户可以访问标签页面查看所有标签及其使用统计

## 文件上传

1. 用户可以通过点击上传按钮选择文件
2. 文件上传后存储在服务器的uploads目录中
3. 文件上传链接会自动插入到微博内容中
