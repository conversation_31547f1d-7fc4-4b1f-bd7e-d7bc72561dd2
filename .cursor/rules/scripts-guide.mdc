---
description: 
globs: 
alwaysApply: true
---
# 项目脚本使用指南

本项目的常用脚本都位于 [scripts](mdc:scripts) 目录中，以下是各脚本的详细说明：

## 🚀 应用运行脚本

### [scripts/run.sh](mdc:scripts/run.sh)
本地开发环境启动脚本，功能包括：
- 自动创建和激活Python虚拟环境
- 安装项目依赖
- 初始化数据库（如果不存在）
- 检查并清理端口占用
- 启动Flask应用

使用方法：
```bash
./scripts/run.sh
```

## 🗄️ 数据库相关脚本

### [scripts/init_db.py](mdc:scripts/init_db.py)
数据库初始化脚本，用于创建数据库表结构
```bash
python scripts/init_db.py
```

### [scripts/backup.py](mdc:scripts/backup.py)
数据库备份脚本，将weibo.db备份到~/Documents/Backup目录
```bash
python scripts/backup.py
```

## 👥 用户管理脚本

### [scripts/add_user.py](mdc:scripts/add_user.py)
交互式用户添加脚本，用于创建新的系统用户
```bash
python scripts/add_user.py
```

## 🚚 部署相关脚本

### [scripts/deploy.sh](mdc:scripts/deploy.sh)
生产环境部署脚本，功能包括：
- 同步代码到远程服务器 (**************)
- 在服务器上安装依赖
- 配置Supervisor进程管理
- 启动/重启应用服务

服务器配置信息：
- 用户: lighthouse
- 主机: **************
- 部署路径: /home/<USER>/flask/weibo

使用方法：
```bash
./scripts/deploy.sh
```

### [scripts/weibo.conf](mdc:scripts/weibo.conf)
Supervisor进程管理配置文件，用于在生产环境中管理Flask应用进程。

## 🔧 开发工具脚本

### [scripts/amend.sh](mdc:scripts/amend.sh)
Git快捷操作脚本，执行以下操作：
- 查看当前状态
- 添加所有变更
- 修正最后一次提交
- 显示操作后状态

使用方法：
```bash
./scripts/amend.sh
```

### [scripts/random.py](mdc:scripts/random.py)
随机微博查看脚本，功能包括：
- 从远程服务器同步数据库
- 随机获取一条微博记录
- 使用macOS系统对话框显示内容

使用方法：
```bash
python scripts/random.py
```

## 📝 使用建议

1. **本地开发**: 使用 `./scripts/run.sh` 启动开发环境
2. **生产部署**: 使用 `./scripts/deploy.sh` 部署到服务器
3. **数据备份**: 定期运行 `python scripts/backup.py` 备份数据
4. **用户管理**: 通过 `python scripts/add_user.py` 添加新用户
5. **快速提交**: 使用 `./scripts/amend.sh` 进行Git操作

## ⚠️ 注意事项

- 确保脚本有执行权限：`chmod +x scripts/*.sh`
- 部署脚本需要配置SSH密钥访问远程服务器
- 备份脚本会在用户文档目录创建Backup文件夹
- random.py脚本仅适用于macOS系统（使用osascript）
