---
description: 
globs: 
alwaysApply: true
---
# 部署信息

本应用的部署通过脚本 [scripts/deploy.sh](mdc:scripts/deploy.sh) 完成。

## 服务器配置

根据 `deploy.sh` 脚本，服务器配置如下：

- **操作系统:** Ubuntu
- **用户:** `lighthouse`
- **主机:** `**************`
- **端口:** `22`
- **远程目录:** `/home/<USER>/flask/weibo`
- **容器化:** 已安装 Docker
- **Web 服务器/反向代理:** 使用 Caddy 支持 HTTPS (安装在宿主机上)

## 部署流程概述

1.  **创建远程目录**: 在服务器上创建 `/home/<USER>/flask/weibo`。
2.  **同步文件**: 使用 `rsync` 将项目文件同步到远程目录，排除 `.git`, `__pycache__`, `*.pyc`, `venv/`, `*.db`。
3.  **创建日志目录**: 在远程目录创建 `logs` 子目录。
4.  **复制 Supervisor 配置**: 将 `scripts/weibo.conf` 复制到服务器的 `/etc/supervisor/conf.d/` 并更新 Supervisor。
5.  **服务器端操作**:
    *   `cd` 进入远程目录。
    *   创建并激活 Python 虚拟环境 (`venv`)。
    *   使用 `pip install -r requirements.txt` 安装依赖。
    *   如果 `weibo.db` 不存在，运行 `python3 init_db.py` 初始化数据库。
    *   使用 `sudo supervisorctl restart weibo` 重启应用（如果 Supervisor 已配置），否则使用 `nohup python3 app.py` 后台启动。
