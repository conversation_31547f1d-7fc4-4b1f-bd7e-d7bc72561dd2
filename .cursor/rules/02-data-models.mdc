---
description: 
globs: 
alwaysApply: true
---
# 数据模型

应用的数据模型定义在 [models.py](mdc:models.py) 文件中。

## 主要模型

### Post 模型

微博内容的数据模型：

```python
class Post(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    content = db.Column(db.Text, nullable=False)
    tags = db.Column(JSON, default=list)
    created_at = db.Column(db.DateTime, nullable=False, default=beijing_time)
```

- `id`: 主键
- `content`: 微博内容
- `tags`: JSON类型字段，存储标签列表
- `created_at`: 创建时间，默认为北京时间

### User 模型

用户数据模型：

```python
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.<PERSON>umn(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
```

- `id`: 主键
- `username`: 用户名
- `password_hash`: 密码的哈希值

## 辅助函数

- `beijing_time()`: 返回带北京时区调整的当前时间
