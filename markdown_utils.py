"""
Markdown工具模块
提供Markdown渲染、清理和验证功能
"""

import markdown
import bleach
from markdown.extensions import codehilite, fenced_code, nl2br, tables, toc
import re


class MarkdownRenderer:
    """Markdown渲染器类"""
    
    def __init__(self):
        """初始化Markdown渲染器"""
        # 配置Markdown扩展
        self.extensions = [
            'fenced_code',     # 代码块支持
            'tables',          # 表格支持
            'codehilite',      # 代码高亮
            'nl2br',           # 换行符转换
        ]

        # 配置扩展参数
        self.extension_configs = {
            'codehilite': {
                'css_class': 'highlight',
                'use_pygments': False,  # 使用CSS类而不是内联样式
            }
        }
        
        # 配置允许的HTML标签和属性（安全设置）
        self.allowed_tags = [
            'p', 'br', 'strong', 'em', 'u', 's', 'del', 'ins',
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
            'ul', 'ol', 'li', 'dl', 'dt', 'dd',
            'blockquote', 'pre', 'code',
            'table', 'thead', 'tbody', 'tr', 'th', 'td',
            'a', 'img',
            'hr', 'div', 'span'
        ]
        
        self.allowed_attributes = {
            'a': ['href', 'title', 'target', 'rel'],
            'img': ['src', 'alt', 'title', 'width', 'height'],
            'code': ['class'],
            'pre': ['class'],
            'div': ['class'],
            'span': ['class'],
            'h1': ['id'], 'h2': ['id'], 'h3': ['id'], 
            'h4': ['id'], 'h5': ['id'], 'h6': ['id'],
        }
        
        # 配置允许的协议
        self.allowed_protocols = ['http', 'https', 'mailto']



    def _postprocess_html(self, html):
        """
        后处理HTML内容，添加换行支持

        Args:
            html (str): 原始HTML

        Returns:
            str: 处理后的HTML
        """
        # 在段落中添加换行符支持
        # 将段落内的单个换行符转换为<br>标签
        def process_paragraph(match):
            content = match.group(1)
            # 将单个换行符转换为<br>标签
            content = re.sub(r'\n', '<br>\n', content)
            return f'<p>{content}</p>'

        # 只处理段落标签，不影响列表等其他元素
        html = re.sub(r'<p>(.*?)</p>', process_paragraph, html, flags=re.DOTALL)

        return html
    
    def render(self, content):
        """
        渲染Markdown内容为HTML

        Args:
            content (str): 原始Markdown内容

        Returns:
            str: 渲染后的安全HTML内容
        """
        if not content:
            return ""

        try:
            # 第一步：Markdown转HTML
            md = markdown.Markdown(
                extensions=self.extensions,
                extension_configs=self.extension_configs
            )
            html = md.convert(content)

            # 第二步：后处理HTML（添加换行支持）
            html = self._postprocess_html(html)

            # 第三步：HTML安全清理
            clean_html = bleach.clean(
                html,
                tags=self.allowed_tags,
                attributes=self.allowed_attributes,
                protocols=self.allowed_protocols,
                strip=True
            )

            # 第四步：链接安全处理
            clean_html = self._process_links(clean_html)

            return clean_html

        except Exception as e:
            # 如果渲染失败，返回转义后的原始内容
            return bleach.clean(content, tags=[], strip=True)
    
    def _process_links(self, html):
        """
        处理链接安全性
        为外部链接添加安全属性
        """
        # 为外部链接添加 target="_blank" 和 rel="noopener noreferrer"
        def add_link_attrs(match):
            href = match.group(1)
            text = match.group(2)
            
            # 检查是否为外部链接
            if href.startswith(('http://', 'https://')) and not self._is_internal_link(href):
                return f'<a href="{href}" target="_blank" rel="noopener noreferrer">{text}</a>'
            else:
                return f'<a href="{href}">{text}</a>'
        
        # 使用正则表达式处理链接
        link_pattern = r'<a href="([^"]*)"[^>]*>([^<]*)</a>'
        return re.sub(link_pattern, add_link_attrs, html)
    
    def _is_internal_link(self, href):
        """
        判断是否为内部链接
        可以根据实际需求自定义判断逻辑
        """
        # 这里可以添加判断内部链接的逻辑
        # 例如检查域名是否为当前站点域名
        return False
    
    def extract_plain_text(self, content):
        """
        从Markdown内容中提取纯文本（用于搜索等功能）
        
        Args:
            content (str): Markdown内容
            
        Returns:
            str: 纯文本内容
        """
        if not content:
            return ""
        
        # 先渲染为HTML
        html = self.render(content)
        
        # 移除所有HTML标签
        plain_text = bleach.clean(html, tags=[], strip=True)
        
        # 清理多余的空白字符
        plain_text = re.sub(r'\s+', ' ', plain_text).strip()
        
        return plain_text
    
    def validate_content(self, content):
        """
        验证Markdown内容
        
        Args:
            content (str): 要验证的内容
            
        Returns:
            dict: 验证结果 {'valid': bool, 'errors': list, 'warnings': list}
        """
        result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        if not content:
            return result
        
        # 检查内容长度
        if len(content) > 10000:  # 10KB限制
            result['errors'].append('内容长度超过限制（最大10KB）')
            result['valid'] = False
        
        # 检查是否包含潜在的恶意内容
        dangerous_patterns = [
            r'<script[^>]*>',
            r'javascript:',
            r'on\w+\s*=',
        ]
        
        for pattern in dangerous_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                result['warnings'].append('内容包含可能不安全的代码')
                break
        
        return result


# 创建全局渲染器实例
markdown_renderer = MarkdownRenderer()


def render_markdown(content):
    """
    便捷函数：渲染Markdown内容
    
    Args:
        content (str): Markdown内容
        
    Returns:
        str: 渲染后的HTML
    """
    return markdown_renderer.render(content)


def extract_plain_text(content):
    """
    便捷函数：提取纯文本
    
    Args:
        content (str): Markdown内容
        
    Returns:
        str: 纯文本
    """
    return markdown_renderer.extract_plain_text(content)


def validate_markdown(content):
    """
    便捷函数：验证Markdown内容
    
    Args:
        content (str): 要验证的内容
        
    Returns:
        dict: 验证结果
    """
    return markdown_renderer.validate_content(content)
