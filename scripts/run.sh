#!/bin/bash

# 颜色输出函数
function echo_info() {
    echo -e "\033[34m[INFO] $1\033[0m"
}

function echo_success() {
    echo -e "\033[32m[SUCCESS] $1\033[0m"
}

function echo_error() {
    echo -e "\033[31m[ERROR] $1\033[0m"
}

# 检查命令是否执行成功
function check_status() {
    if [ $? -eq 0 ]; then
        echo_success "$1"
    else
        echo_error "$2"
        exit 1
    fi
}

# 检查端口是否被占用并处理
function check_and_kill_port() {
    local port=$1
    if lsof -i :$port > /dev/null 2>&1; then
        echo_info "端口 $port 已被占用，正在终止占用进程..."
        lsof -ti :$port | xargs kill -9
        check_status "端口占用进程已终止" "终止端口占用进程失败"
    fi
}

# 检查 Python 是否安装
if ! command -v python3 &> /dev/null
then
    echo_error "未找到 Python3，请先安装 Python3"
    exit 1
fi

# 检查虚拟环境是否存在
if [ ! -d "venv" ]; then
    echo_info "创建虚拟环境..."
    python3 -m venv venv
    check_status "虚拟环境创建成功" "虚拟环境创建失败"
fi

# 激活虚拟环境
echo_info "激活虚拟环境..."
source venv/bin/activate
check_status "虚拟环境激活成功" "虚拟环境激活失败"

# 安装依赖
echo_info "安装项目依赖..."
pip install -r requirements.txt
check_status "依赖安装成功" "依赖安装失败"

# 同步云端数据库
# echo_info "正在从云端同步数据库..."
# scp lighthouse@**************:/home/<USER>/flask/weibo/weibo.db ./
# check_status "数据库同步成功" "数据库同步失败"

# 检查数据库是否存在
if [ ! -f "weibo.db" ]; then
    echo_info "初始化数据库..."
    python scripts/init_db.py
    check_status "数据库初始化成功" "数据库初始化失败"
fi

# 检查并清理端口占用
check_and_kill_port 5001

# 启动应用
echo_info "启动 Flask 应用..."
python3 app.py