from models import db, User
from werkzeug.security import generate_password_hash

def add_user(username, password):
    try:
        # 检查用户是否已存在
        existing_user = User.query.filter_by(username=username).first()
        if existing_user:
            print(f"用户 '{username}' 已存在。")
            return

        # 创建新用户
        new_user = User(
            username=username,
            password_hash=generate_password_hash(password)
        )
        db.session.add(new_user)
        db.session.commit()
        print(f"用户 '{username}' 已成功添加。")
    except Exception as e:
        print(f"添加用户时出错：{e}")

if __name__ == '__main__':
    # 提示用户输入用户名和密码
    username = input("请输入用户名: ")
    password = input("请输入密码: ")

    # 初始化数据库并添加用户
    from app import app
    with app.app_context():
        db.init_app(app)
        add_user(username, password)