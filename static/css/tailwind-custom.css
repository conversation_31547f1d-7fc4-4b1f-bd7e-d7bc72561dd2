/* Tailwind CSS 自定义补充样式 */

/* 自定义 min-height 类 */
.min-h-250 {
    min-height: 250px;
}

.min-h-300 {
    min-height: 300px;
}

/* 响应式 min-height 类 */
@media (min-width: 768px) {
    .md\:min-h-250 {
        min-height: 250px;
    }

    .md\:min-h-300 {
        min-height: 300px;
    }
}

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    overflow-y: scroll; /* 始终显示垂直滚动条，避免页面布局跳动 */
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
    background: #f8fafc;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    font-size: 16px;
}

/* 模态框动画 */
@keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* 滑入动画 */
@keyframes slide-in {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 模态框滑入动画 */
@keyframes modal-slide-in {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 动画类 */
.animate-fade-in {
    animation: fade-in 0.3s ease;
}

.animate-slide-in {
    animation: slide-in 0.6s ease;
}

.animate-modal-slide-in {
    animation: modal-slide-in 0.3s ease;
}

.animate-fade-in {
    animation: fade-in 0.3s ease;
}

.animate-slide-in {
    animation: slide-in 0.3s ease;
}

/* 模态框显示状态 */
.modal {
    display: none;
}

.modal.show {
    display: block !important;
}

/* 滚动条美化 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 高亮搜索结果 */
.highlight {
    background: linear-gradient(135deg, rgba(240, 147, 251, 0.3) 0%, rgba(102, 126, 234, 0.3) 100%);
    padding: 2px 4px;
    border-radius: 4px;
    font-weight: 600;
}

/* 加载动画 */
@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* 标签组件样式 */
.tag {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 9999px;
    transition: all 0.3s ease;
    white-space: nowrap;
    text-decoration: none;
}

.tag:hover {
    transform: translateY(-1px) scale(1.05);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* 标签移除按钮样式 */
.tag .remove-tag {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    margin-left: 0.25rem;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    border-radius: 50%;
    transition: all 0.2s ease;
    background: rgba(255, 255, 255, 0.2);
}

.tag .remove-tag:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* 确保标签容器的样式 */
#tagsContainer,
#editTagsContainer {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

/* 标签输入框焦点状态 */
.tags-input-wrapper:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 文本截断工具类 */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .modal .bg-white {
        margin: 10% auto;
        width: 95%;
    }
    
    .modal .p-8 {
        padding: 1.5rem;
    }
    
    .modal .px-8 {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

@media (max-width: 480px) {
    .modal .bg-white {
        margin: 15% auto;
    }
    
    .modal .p-8 {
        padding: 1rem;
    }
    
    .modal .px-8 {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}
