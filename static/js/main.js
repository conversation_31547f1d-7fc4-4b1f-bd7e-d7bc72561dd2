// 编辑微博相关变量
const editModal = document.getElementById('editModal');
const editForm = document.getElementById('editForm');
const editTagInput = document.getElementById('editTagInput');
const editTagsContainer = document.getElementById('editTagsContainer');
const editTagsField = document.getElementById('editTagsField');
let currentPostId = null;
let editTagManager; // 编辑模态框的标签管理器

function showEditModal() {
    document.body.style.overflow = 'hidden'; // 防止背景滚动
    editModal.classList.add('show');
}

function hideEditModal() {
    document.body.style.overflow = '';
    editModal.classList.remove('show');
    // 清空表单数据
    editForm.reset();
    editTagManager.clearTags();
    currentPostId = null;
}

// 设置当前时间到时间字段
function setCurrentTime() {
    const now = new Date();
    // 获取本地时间并格式化为 datetime-local 要求的格式
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');

    const formattedTime = `${year}-${month}-${day}T${hours}:${minutes}`;
    document.getElementById('editPostTime').value = formattedTime;
}

document.addEventListener('DOMContentLoaded', function () {
    // 初始化标签管理器
    const tagInput = document.getElementById('tagInput');
    const tagsContainer = document.getElementById('tagsContainer');
    const tagsField = document.getElementById('tagsField');
    
    // 创建发布微博的标签管理器
    const postTagManager = new TagManager({
        tagInput: tagInput,
        tagsContainer: tagsContainer,
        tagsField: tagsField
    });
    
    // 创建编辑微博的标签管理器
    editTagManager = new TagManager({
        tagInput: editTagInput,
        tagsContainer: editTagsContainer,
        tagsField: editTagsField
    });
    
    // 初始化编辑模态框事件
    editModal.querySelectorAll('[data-dismiss="modal"]').forEach(button => {
        button.addEventListener('click', hideEditModal);
    });

    // Now按钮事件监听器
    const setCurrentTimeBtn = document.getElementById('setCurrentTimeBtn');
    if (setCurrentTimeBtn) {
        setCurrentTimeBtn.addEventListener('click', setCurrentTime);
    }

    // 点击模态框背景时关闭
    editModal.addEventListener('click', function (event) {
        // 仅允许通过关闭按钮关闭对话框，不再响应点击背景关闭
        if (event.target.classList.contains('close-button')) {
            hideEditModal();
        }
    });

    // --- 新的高亮函数 ---
    function highlightKeywords(element, keywords) {
        if (!keywords || keywords.length === 0 || !element) return;

        let innerHTML = element.innerHTML;
        // 避免在高亮标签内部重复高亮
        const marker = "__HIGHLIGHT_MARKER__";
        const highlightedContent = innerHTML.replace(/<span class="highlight">([^<]+)<\/span>/gi, marker + "$1" + marker);

        let tempContent = highlightedContent;
        keywords.forEach(keyword => {
            if (keyword.trim() === '') return; // 跳过空关键词
            // 转义正则表达式特殊字符
            const escapedKeyword = keyword.replace(/[.*+?^${}()|[\\]\\\\]/g, '$&');
            // 创建不区分大小写的正则表达式
            const regex = new RegExp(`(${escapedKeyword})(?![^<>]*>)`, 'gi'); // (?![^<>]*>) 确保不在HTML标签内匹配
            
            // 替换文本节点中的关键词
            tempContent = tempContent.replace(regex, '<span class="highlight">$1</span>');
        });
        
        // 恢复之前的高亮标记
        element.innerHTML = tempContent.replace(new RegExp(marker + "([^\" + marker + \"]+)" + marker, "g"), '<span class="highlight">$1</span>');
    }

    // 应用高亮
    const searchQuery = new URLSearchParams(window.location.search).get('q');
    if (searchQuery) {
        const keywords = searchQuery.split(' ').filter(k => k.trim() !== ''); // 分割并过滤空关键词
        if (keywords.length > 0) {
            // 高亮微博正文内容
            document.querySelectorAll('.post-content').forEach(contentDiv => {
                highlightKeywords(contentDiv, keywords);
            });
            // 高亮标签文本
            document.querySelectorAll('.tags-container .tag').forEach(tagElement => {
                highlightKeywords(tagElement, keywords);
            });
        }
    }
    // --- 高亮逻辑结束 ---

    // 搜索功能
    const searchButton = document.getElementById('searchButton');
    const searchInput = document.getElementById('searchInput');

    // 获取当前标签信息
    function getCurrentTag() {
        // 检查URL中是否有tag参数
        const urlParams = new URLSearchParams(window.location.search);
        const tagParam = urlParams.get('tag');
        if (tagParam) {
            return tagParam;
        }
        
        // 检查页面上是否显示当前筛选标签
        const currentFilterElement = document.querySelector('.current-filter .current-tag');
        if (currentFilterElement) {
            const tagText = currentFilterElement.textContent.trim();
            if (tagText === '无标签') {
                return 'no_tags';
            }
            return tagText;
        }
        
        return null;
    }

    // 构建搜索URL，包含当前标签
    function buildSearchUrl(query) {
        const currentTag = getCurrentTag();
        let url = `/?q=${encodeURIComponent(query)}`;
        if (currentTag) {
            url += `&tag=${encodeURIComponent(currentTag)}`;
        }
        return url;
    }

    searchButton.addEventListener('click', function() {
        const query = searchInput.value.trim();
        if (query) {
            window.location.href = buildSearchUrl(query);
        }
    });

    // 支持回车键搜索
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            const query = this.value.trim();
            if (query) {
                window.location.href = buildSearchUrl(query);
            }
        }
    });

    // 发布按钮点击事件
    const postButton = document.getElementById('postButton');
    const postModal = document.getElementById('postModal');
    const postForm = document.getElementById('postForm');

    function showPostModal() {
        postModal.classList.add('show');
    }

    function hidePostModal() {
        postModal.classList.remove('show');
        postForm.reset();
        postTagManager.clearTags();
    }

    postButton.addEventListener('click', showPostModal);

    // 关闭模态框事件
    postModal.querySelectorAll('[data-dismiss="modal"]').forEach(button => {
        button.addEventListener('click', hidePostModal);
    });

    // 禁用外部点击关闭发布对话框
    postModal.addEventListener('click', function (event) {
        // 仅允许通过关闭按钮关闭对话框
        if (event.target.classList.contains('close-button')) {
            hidePostModal();
        }
    });

    // AJAX表单提交
    postForm.addEventListener('submit', function (e) {
        e.preventDefault();

        // 检查未添加的标签
        postTagManager.processRemainingTag();

        const formData = new FormData(this);
        formData.append('tags', JSON.stringify(postTagManager.getTags()));

        fetch(window.API_URLS.createPost, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams(formData)
        })
        .then(response => {
            if (!response.ok) {
                return response.text().then(text => {
                    try {
                        const errorData = JSON.parse(text);
                        throw new Error(errorData.message || '请求失败');
                    } catch {
                        throw new Error(`服务器错误 (${response.status}): ${text.slice(0, 100)}`);
                    }
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // 不再刷新整个页面，而是动态添加新微博
                const newPost = data.post;
                
                // 创建新的微博卡片
                const postCard = document.createElement('div');
                postCard.className = 'card bg-white rounded-2xl p-8 shadow-sm border border-gray-200 relative overflow-hidden md:p-6';
                postCard.setAttribute('data-post-id', newPost.id);

                // 构建微博卡片内容
                let tagsHtml = '';
                newPost.tags.forEach(tag => {
                    const isActive = newPost.current_tag === tag ? 'active bg-gradient-to-r from-indigo-500 to-purple-600 text-white border-transparent' : '';
                    tagsHtml += `<a href="/?tag=${encodeURIComponent(tag)}" class="tag inline-flex items-center px-3 py-1 text-xs font-semibold bg-gradient-to-br from-indigo-50 to-purple-50 text-indigo-600 no-underline rounded-full border border-indigo-200 transition-all duration-300 hover:bg-gradient-to-br hover:from-indigo-100 hover:to-purple-100 hover:-translate-y-0.5 hover:shadow-sm ${isActive}">${tag}</a>`;
                });

                postCard.innerHTML = `
                    <div class="card-header mb-0">
                        <div class="header-row flex items-center justify-between mb-4">
                            <div class="post-time text-xs text-gray-500 font-medium">${newPost.created_at}</div>
                            <div class="btn-group flex gap-2">
                                <button class="btn-secondary px-3 py-1 text-xs font-semibold border-0 rounded-xl cursor-pointer transition-all duration-300 bg-indigo-600 text-white hover:bg-indigo-700 hover:-translate-y-0.5 edit-post md:px-2 md:py-1 md:text-xs md:min-w-[40px]" data-post-id="${newPost.id}">编辑</button>
                                <button class="btn-danger px-3 py-1 text-xs font-semibold border-0 rounded-xl cursor-pointer transition-all duration-300 bg-red-500 text-white hover:bg-red-600 hover:-translate-y-0.5 delete-post md:px-2 md:py-1 md:text-xs md:min-w-[40px]" data-post-id="${newPost.id}">删除</button>
                            </div>
                        </div>
                        <div class="post-content text-base leading-relaxed text-gray-800 mb-6 break-words markdown-content" id="content-${newPost.id}">${newPost.rendered_content}</div>
                        <div class="tags-container flex flex-wrap gap-2">
                            ${tagsHtml}
                        </div>
                    </div>
                `;
                
                // 将新微博添加到列表顶部
                const postsContainer = document.querySelector('.posts-list');
                if (postsContainer) {
                    const firstCard = postsContainer.querySelector('.card');
                    if (firstCard) {
                        postsContainer.insertBefore(postCard, firstCard);
                    } else {
                        postsContainer.appendChild(postCard);
                    }
                } else {
                    // 如果没有posts-list容器，则添加到container中分页导航之前
                    const container = document.querySelector('.container');
                    const pagination = container.querySelector('.pagination');
                    if (pagination) {
                        container.insertBefore(postCard, pagination);
                    } else {
                        container.appendChild(postCard);
                    }
                }
            } else {
                alert('发布失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('发布失败，请重试');
        })
        .finally(() => {
            hidePostModal();
        });
    });

    // 删除微博模态框控制
    const deleteModal = document.getElementById('deleteConfirmModal');
    const deleteForm = document.getElementById('deletePostForm');
    const modalBody = deleteModal.querySelector('.modal-body');

    function showDeleteModal() {
        deleteModal.classList.add('show');
    }

    function hideDeleteModal() {
        deleteModal.classList.remove('show');
    }

    // 关闭删除模态框事件
    deleteModal.querySelectorAll('[data-dismiss="modal"]').forEach(button => {
        button.addEventListener('click', hideDeleteModal);
    });

    deleteModal.addEventListener('click', function (event) {
        if (event.target === deleteModal) {
            hideDeleteModal();
        }
    });

    // 事件委托处理删除和编辑操作
    document.addEventListener('click', function (event) {
        const target = event.target;

        // 删除微博处理
        if (target.classList.contains('delete-post')) {
            const postId = target.getAttribute('data-post-id');
            modalBody.textContent = '确定要删除这条微博吗？';
            showDeleteModal();

            const confirmDeleteBtn = deleteModal.querySelector('.modal-footer .btn-danger');
            confirmDeleteBtn.onclick = function (e) {
                e.preventDefault();
                fetch(`/post/${postId}/delete`, {
                    method: 'POST'
                })
                .then(response => {
                    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                    const postCard = target.closest('.card');
                    postCard.remove();
                    hideDeleteModal();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除微博失败，请重试');
                    hideDeleteModal();
                });
            };
        }

        // 编辑微博处理
        if (target.classList.contains('edit-post')) {
            const postId = target.getAttribute('data-post-id');
            currentPostId = postId; // 设置当前编辑的微博ID
            const postCard = target.closest('.card');
            const contentElement = postCard.querySelector('.post-content');
            const tagsElements = postCard.querySelectorAll('.tags-container .tag');
            const postTimeElement = postCard.querySelector('.post-time');

            // 填充表单
            editForm.querySelector('textarea').value = contentElement.textContent.trim();

            // 从标签容器中提取所有链接作为标签，因为重构后标签不再有简单的 'tag' 类名
            const tagLinks = postCard.querySelectorAll('.tags-container a[href*="tag="]');
            const tags = Array.from(tagLinks).map(link => {
                // 尝试从 span.relative.z-10 中获取文本，如果没有则直接从链接元素获取
                const tagTextSpan = link.querySelector('span.relative.z-10');
                return tagTextSpan ? tagTextSpan.textContent.trim() : link.textContent.trim();
            });
            editTagManager.setTags(tags);
            
            // 设置发布时间
            const postTimeText = postTimeElement.textContent.trim();
            // 直接使用页面上显示的时间（已经是北京时间），将其转换为HTML datetime-local要求的格式
            // 格式转换: "YYYY-MM-DD HH:MM" => "YYYY-MM-DDTHH:MM"
            const formattedTime = postTimeText.replace(' ', 'T');
            document.getElementById('editPostTime').value = formattedTime;

            // 显示编辑对话框
            showEditModal();

            // 处理表单提交
            editForm.onsubmit = function (e) {
                e.preventDefault();
                
                if (!currentPostId) {
                    alert('编辑失败：未找到微博ID');
                    return;
                }
                
                // 检查并添加输入框中未确认的标签
                editTagManager.processRemainingTag();
                
                // 创建 FormData 对象
                const formData = new FormData(this);
                
                // 手动将更新后的标签数组添加到 formData
                formData.append('tags', JSON.stringify(editTagManager.getTags()));
                
                // 获取并添加发布时间
                const editPostTime = document.getElementById('editPostTime').value;
                if (editPostTime) {
                    formData.append('created_at', editPostTime);
                }
                
                fetch(`/post/${currentPostId}/update`, {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        // 更新页面上的微博内容
                        const postContent = document.getElementById(`content-${currentPostId}`);
                        const postCard = postContent.closest('.card');
                        const tagsContainer = postCard.querySelector('.tags-container');
                        const postTimeElement = postCard.querySelector('.post-time');
                        
                        // 更新内容 - 使用后端返回的渲染后内容
                        postContent.innerHTML = data.post.rendered_content;
                        
                        // 直接使用后端返回的格式化时间
                        postTimeElement.textContent = data.post.created_at;
                        
                        // 更新标签
                        tagsContainer.innerHTML = '';
                        editTagManager.getTags().forEach(tag => {
                            const tagElement = document.createElement('a');
                            tagElement.href = `/?tag=${encodeURIComponent(tag)}`;
                            tagElement.className = 'tag';
                            tagElement.textContent = tag;
                            tagsContainer.appendChild(tagElement);
                        });
                        
                        hideEditModal();
                    } else {
                        alert('编辑失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('编辑失败，请重试');
                });
            };
        }
    });
});