#!/usr/bin/env python3
"""
Markdown功能测试脚本
测试Markdown渲染、验证和安全性
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from markdown_utils import render_markdown, validate_markdown, extract_plain_text


def test_basic_markdown():
    """测试基本Markdown功能"""
    print("🧪 测试基本Markdown功能...")
    
    test_cases = [
        # 基本格式
        ("**粗体文本**", "<p><strong>粗体文本</strong></p>"),
        ("*斜体文本*", "<p><em>斜体文本</em></p>"),
        ("`代码`", "<p><code>代码</code></p>"),
        
        # 标题
        ("# 一级标题", "<h1>一级标题</h1>"),
        ("## 二级标题", "<h2>二级标题</h2>"),
        
        # 列表
        ("- 列表项1\n- 列表项2", "<ul>\n<li>列表项1</li>\n<li>列表项2</li>\n</ul>"),
        
        # 链接
        ("[链接文本](https://example.com)", '<p><a href="https://example.com" target="_blank" rel="noopener noreferrer">链接文本</a></p>'),
        
        # 普通文本（应该正常显示）
        ("这是普通文本", "<p>这是普通文本</p>"),
        
        # 换行
        ("第一行\n第二行", "<p>第一行<br"),
    ]
    
    passed = 0
    failed = 0
    
    for i, (input_text, expected_contains) in enumerate(test_cases, 1):
        try:
            result = render_markdown(input_text)
            # 检查结果是否包含期望的内容（简化检查）
            if expected_contains.replace('\n', '').replace(' ', '') in result.replace('\n', '').replace(' ', ''):
                print(f"  ✅ 测试 {i}: 通过")
                passed += 1
            else:
                print(f"  ❌ 测试 {i}: 失败")
                print(f"     输入: {input_text}")
                print(f"     期望包含: {expected_contains}")
                print(f"     实际输出: {result}")
                failed += 1
        except Exception as e:
            print(f"  ❌ 测试 {i}: 异常 - {e}")
            failed += 1
    
    print(f"  📊 基本功能测试结果: {passed} 通过, {failed} 失败")
    return failed == 0


def test_security():
    """测试安全性"""
    print("\n🔒 测试安全性...")
    
    dangerous_inputs = [
        "<script>alert('xss')</script>",
        "<img src=x onerror=alert('xss')>",
        "<iframe src='javascript:alert(1)'></iframe>",
    ]
    
    passed = 0
    failed = 0
    
    for i, dangerous_input in enumerate(dangerous_inputs, 1):
        try:
            result = render_markdown(dangerous_input)
            # 检查是否包含危险内容
            if 'script' not in result.lower() and 'javascript:' not in result.lower() and 'onerror' not in result.lower():
                print(f"  ✅ 安全测试 {i}: 通过 - 危险内容已被过滤")
                passed += 1
            else:
                print(f"  ❌ 安全测试 {i}: 失败 - 危险内容未被过滤")
                print(f"     输入: {dangerous_input}")
                print(f"     输出: {result}")
                failed += 1
        except Exception as e:
            print(f"  ❌ 安全测试 {i}: 异常 - {e}")
            failed += 1
    
    print(f"  📊 安全性测试结果: {passed} 通过, {failed} 失败")
    return failed == 0


def test_validation():
    """测试内容验证"""
    print("\n✅ 测试内容验证...")
    
    test_cases = [
        ("正常内容", True),
        ("", True),  # 空内容应该有效
        ("a" * 5000, True),  # 5KB内容应该有效
        ("a" * 15000, False),  # 15KB内容应该无效
    ]
    
    passed = 0
    failed = 0
    
    for i, (content, should_be_valid) in enumerate(test_cases, 1):
        try:
            result = validate_markdown(content)
            if result['valid'] == should_be_valid:
                print(f"  ✅ 验证测试 {i}: 通过")
                passed += 1
            else:
                print(f"  ❌ 验证测试 {i}: 失败")
                print(f"     内容长度: {len(content)}")
                print(f"     期望有效性: {should_be_valid}")
                print(f"     实际有效性: {result['valid']}")
                print(f"     错误信息: {result.get('errors', [])}")
                failed += 1
        except Exception as e:
            print(f"  ❌ 验证测试 {i}: 异常 - {e}")
            failed += 1
    
    print(f"  📊 验证测试结果: {passed} 通过, {failed} 失败")
    return failed == 0


def test_plain_text_extraction():
    """测试纯文本提取"""
    print("\n📝 测试纯文本提取...")
    
    test_cases = [
        ("**粗体** 和 *斜体*", "粗体 和 斜体"),
        ("# 标题\n\n段落内容", "标题 段落内容"),
        ("[链接](https://example.com)", "链接"),
        ("`代码` 和普通文本", "代码 和普通文本"),
    ]
    
    passed = 0
    failed = 0
    
    for i, (markdown_text, expected_plain) in enumerate(test_cases, 1):
        try:
            result = extract_plain_text(markdown_text)
            if expected_plain in result:
                print(f"  ✅ 提取测试 {i}: 通过")
                passed += 1
            else:
                print(f"  ❌ 提取测试 {i}: 失败")
                print(f"     输入: {markdown_text}")
                print(f"     期望包含: {expected_plain}")
                print(f"     实际输出: {result}")
                failed += 1
        except Exception as e:
            print(f"  ❌ 提取测试 {i}: 异常 - {e}")
            failed += 1
    
    print(f"  📊 纯文本提取测试结果: {passed} 通过, {failed} 失败")
    return failed == 0


def test_compatibility():
    """测试向后兼容性"""
    print("\n🔄 测试向后兼容性...")
    
    # 模拟现有的普通文本内容
    plain_texts = [
        "这是一条普通的微博内容",
        "包含换行的\n普通文本",
        "包含特殊字符的文本: @用户 #标签 https://example.com",
        "多行文本\n第二行\n第三行",
    ]
    
    passed = 0
    failed = 0
    
    for i, plain_text in enumerate(plain_texts, 1):
        try:
            result = render_markdown(plain_text)
            # 普通文本应该被正确渲染为HTML，不应该出错
            if result and '<p>' in result:
                print(f"  ✅ 兼容性测试 {i}: 通过 - 普通文本正确渲染")
                passed += 1
            else:
                print(f"  ❌ 兼容性测试 {i}: 失败 - 普通文本渲染异常")
                print(f"     输入: {plain_text}")
                print(f"     输出: {result}")
                failed += 1
        except Exception as e:
            print(f"  ❌ 兼容性测试 {i}: 异常 - {e}")
            failed += 1
    
    print(f"  📊 兼容性测试结果: {passed} 通过, {failed} 失败")
    return failed == 0


def main():
    """运行所有测试"""
    print("🚀 开始Markdown功能测试\n")
    
    all_passed = True
    
    # 运行所有测试
    tests = [
        test_basic_markdown,
        test_security,
        test_validation,
        test_plain_text_extraction,
        test_compatibility,
    ]
    
    for test_func in tests:
        if not test_func():
            all_passed = False
    
    # 总结
    print("\n" + "="*50)
    if all_passed:
        print("🎉 所有测试通过！Markdown功能已准备就绪。")
        print("\n✨ 功能特性:")
        print("  • 支持基本Markdown语法（粗体、斜体、标题、列表、链接等）")
        print("  • 安全的HTML渲染，防止XSS攻击")
        print("  • 内容长度验证")
        print("  • 向后兼容普通文本")
        print("  • 纯文本提取功能")
        return 0
    else:
        print("❌ 部分测试失败，请检查实现。")
        return 1


if __name__ == "__main__":
    exit(main())
